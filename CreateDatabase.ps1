# PowerShell脚本用于创建MyPetShop数据库
# 请确保SQL Server已安装并运行

Write-Host "正在创建MyPetShop数据库..." -ForegroundColor Green

# 检查SQL Server是否可用
try {
    $sqlcmd = Get-Command sqlcmd -ErrorAction Stop
    Write-Host "找到SQL Server命令行工具" -ForegroundColor Green
} catch {
    Write-Host "错误: 未找到sqlcmd命令。请确保SQL Server已正确安装。" -ForegroundColor Red
    exit 1
}

# 执行数据库脚本
try {
    Write-Host "正在执行数据库脚本..." -ForegroundColor Yellow
    sqlcmd -S ".\SQLEXPRESS" -i "Database_Scripts\Shop_sql2.sql"
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "数据库创建成功！" -ForegroundColor Green
        Write-Host "数据库名称: MyPetShop" -ForegroundColor Cyan
        Write-Host "默认用户: admin (密码: 123)" -ForegroundColor Cyan
        Write-Host "测试用户: Jack (密码: 123)" -ForegroundColor Cyan
    } else {
        Write-Host "数据库创建失败，请检查错误信息。" -ForegroundColor Red
    }
} catch {
    Write-Host "执行数据库脚本时出错: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "按任意键继续..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
