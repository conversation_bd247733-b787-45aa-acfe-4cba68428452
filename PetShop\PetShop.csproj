<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props" Condition="Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{8B5C2B4D-6F6C-40C5-B3F1-E22B8F6ECDA4}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>PetShop</RootNamespace>
    <AssemblyName>PetShop</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <MvcBuildViews>false</MvcBuildViews>
    <UseIISExpress>true</UseIISExpress>
    <Use64BitIISExpress />
    <IISExpressSSLPort />
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="EntityFramework, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.2.0\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.2.0\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web.Abstractions" />
    <Reference Include="System.Web.Routing" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="Microsoft.Web.Infrastructure, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.Web.Infrastructure.1.0.0.0\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http">
    </Reference>
    <Reference Include="System.Net.Http.WebRequest">
    </Reference>
    <Reference Include="Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=2.0.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\lib\net45\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="allproduct.aspx" />
    <Content Include="h.Master" />
    <Content Include="Images\arrow.gif" />
    <Content Include="Images\close.gif" />
    <Content Include="Images\google_logo.gif" />
    <Content Include="Images\logo.gif" />
    <Content Include="Images\maxsize.gif" />
    <Content Include="Images\minsize.gif" />
    <Content Include="Images\new_flash.gif" />
    <Content Include="Images\searchbutton.gif" />
    <Content Include="Images\spacer.gif" />
    <Content Include="Images\xml.gif" />
    <Content Include="index.aspx" />
    <Content Include="index_m.aspx" />
    <Content Include="login.aspx" />
    <Content Include="myinfo.aspx" />
    <Content Include="Prod_Images\11.png" />
    <Content Include="Prod_Images\a1.gif" />
    <Content Include="Prod_Images\ant.gif" />
    <Content Include="Prod_Images\b1.gif" />
    <Content Include="Prod_Images\butterfly.gif" />
    <Content Include="Prod_Images\cat.gif" />
    <Content Include="Prod_Images\domestic.gif" />
    <Content Include="Prod_Images\eucalyptus.gif" />
    <Content Include="Prod_Images\flowerloving.gif" />
    <Content Include="Prod_Images\meno.gif" />
    <Content Include="Prod_Images\panda.gif" />
    <Content Include="Prod_Images\pointy.gif" />
    <Content Include="Prod_Images\Saola.png" />
    <Content Include="Prod_Images\zebra.gif" />
    <Content Include="productDetail.aspx" />
    <Content Include="productSearch.aspx" />
    <Content Include="q.Master" />
    <Content Include="userAdd.aspx" />
    <Content Include="Web.config" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="allproduct.aspx.cs">
      <DependentUpon>allproduct.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="allproduct.aspx.designer.cs">
      <DependentUpon>allproduct.aspx</DependentUpon>
    </Compile>
    <Compile Include="CartItem.cs">
      <DependentUpon>Model1.tt</DependentUpon>
    </Compile>
    <Compile Include="Category.cs">
      <DependentUpon>Model1.tt</DependentUpon>
    </Compile>
    <Compile Include="Customer.cs">
      <DependentUpon>Model1.tt</DependentUpon>
    </Compile>
    <Compile Include="h.Master.cs">
      <DependentUpon>h.Master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="h.Master.designer.cs">
      <DependentUpon>h.Master</DependentUpon>
    </Compile>
    <Compile Include="index.aspx.cs">
      <DependentUpon>index.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="index.aspx.designer.cs">
      <DependentUpon>index.aspx</DependentUpon>
    </Compile>
    <Compile Include="index_m.aspx.cs">
      <DependentUpon>index_m.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="index_m.aspx.designer.cs">
      <DependentUpon>index_m.aspx</DependentUpon>
    </Compile>
    <Compile Include="login.aspx.cs">
      <DependentUpon>login.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="login.aspx.designer.cs">
      <DependentUpon>login.aspx</DependentUpon>
    </Compile>
    <Compile Include="Model1.Context.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Model1.Context.tt</DependentUpon>
    </Compile>
    <Compile Include="Model1.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Model1.tt</DependentUpon>
    </Compile>
    <Compile Include="Model1.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Model1.edmx</DependentUpon>
    </Compile>
    <Compile Include="myinfo.aspx.cs">
      <DependentUpon>myinfo.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="myinfo.aspx.designer.cs">
      <DependentUpon>myinfo.aspx</DependentUpon>
    </Compile>
    <Compile Include="Order.cs">
      <DependentUpon>Model1.tt</DependentUpon>
    </Compile>
    <Compile Include="OrderItem.cs">
      <DependentUpon>Model1.tt</DependentUpon>
    </Compile>
    <Compile Include="Product.cs">
      <DependentUpon>Model1.tt</DependentUpon>
    </Compile>
    <Compile Include="productDetail.aspx.cs">
      <DependentUpon>productDetail.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="productDetail.aspx.designer.cs">
      <DependentUpon>productDetail.aspx</DependentUpon>
    </Compile>
    <Compile Include="productSearch.aspx.cs">
      <DependentUpon>productSearch.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="productSearch.aspx.designer.cs">
      <DependentUpon>productSearch.aspx</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="q.Master.cs">
      <DependentUpon>q.Master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="q.Master.designer.cs">
      <DependentUpon>q.Master</DependentUpon>
    </Compile>
    <Compile Include="Supplier.cs">
      <DependentUpon>Model1.tt</DependentUpon>
    </Compile>
    <Compile Include="userAdd.aspx.cs">
      <DependentUpon>userAdd.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="userAdd.aspx.designer.cs">
      <DependentUpon>userAdd.aspx</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
    <None Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
    <None Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="App_Data\" />
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <Target Name="MvcBuildViews" AfterTargets="AfterBuild" Condition="'$(MvcBuildViews)'=='true'">
    <AspNetCompiler VirtualPath="temp" PhysicalPath="$(WebProjectOutputDir)" />
  </Target>
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>0</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost:44362/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>这台计算机上缺少此项目引用的 NuGet 程序包。使用"NuGet 程序包还原"可下载这些程序包。有关更多信息，请参见 http://go.microsoft.com/fwlink/?LinkID=322105。缺少的文件是 {0}。</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props'))" />
  </Target>
</Project>
