﻿<%@ Master Language="C#" AutoEventWireup="true" CodeBehind="h.master.cs" Inherits="Web1.h" %>

<!DOCTYPE html>

<html>
<head runat="server">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title></title>

    <style type="text/css">
        .auto-style1 {
            width: 800px;
        }
        .auto-style2 {
            width: 100%;
        }
        .auto-style3 {
            width: 78px;
        }
        .auto-style5 {
            background-color: #00FFCC;
            height: 16px;
        }
        .auto-style7 {
            background-color:aliceblue;
            
        }
        .auto-left {
            background-color:cornsilk;
            width: 200px;
            height: 400px;
        }
        .auto-right {
            
            width: 600px;
            height: 400px;
        }
        .auto-style12 {
            width: 65px;
            height: 31px;
        }
        .auto-style13 {
            font-size: xx-large;
        }
    </style>

</head>
<body>
    <form id="form1" runat="server">
        <div>
   
            <table cellpadding="0" cellspacing="0" class="auto-style1">
                <tr>
                    <td colspan="2">
                        <table cellpadding="0" cellspacing="0" class="auto-style2">
                            <tr>
                                <td class="auto-style3">
                                    <img alt="" class="auto-style12" src="Images/logo.gif" /></td>
                                <td>
                                    <table cellpadding="0" cellspacing="0" class="auto-style2">
                                        <tr>
                                            <td class="auto-style5">
                                                &nbsp;</td>

                                                              <td class="auto-style5">
                                            </td>
                                            <td class="auto-style5">
                                                &nbsp;</td>
                                            <td class="auto-style5">
                                                &nbsp;</td>
                                            <td class="auto-style5">
                                                &nbsp;</td>
                                            <td class="auto-style5">
                                                &nbsp;</td>
                                        </tr>
                                        <tr>
                                            <td colspan="5" class="auto-style13">
                                                后台管理系统</td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td colspan="2">
                        <asp:Label ID="Label1" runat="server" Text="Label"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td class="auto-left" valign="top"><br />
                        <br />
                        <asp:TreeView ID="TreeView1" runat="server">
                            <Nodes>
                                <asp:TreeNode NavigateUrl="~/index_m.aspx" Text="manage" Value="manage">
                                    <asp:TreeNode Text="用户信息管理" Value="新建节点">
                                        <asp:TreeNode NavigateUrl="custList.aspx" Text="用户列表" Value="用户列表"></asp:TreeNode>
                                        <asp:TreeNode NavigateUrl="~/userAdd.aspx" Text="添加用户" Value="添加user"></asp:TreeNode>
                                    </asp:TreeNode>
                                    <asp:TreeNode Text="商品信息管理" Value="商品信息管理">
                                        <asp:TreeNode NavigateUrl="productList.aspx" Text="商品列表" Value="产品列表"></asp:TreeNode>
                                        <asp:TreeNode NavigateUrl="productAdd.aspx" Text="添加商品" Value="添加product"></asp:TreeNode>
                                    </asp:TreeNode>
                                    <asp:TreeNode Text="商品分类管理" Value="商品分类管理"></asp:TreeNode>
                                    <asp:TreeNode Text="订单管理" Value="订单管理"></asp:TreeNode>
                                    <asp:TreeNode Text="公告管理" Value="公告管理"></asp:TreeNode>
                                </asp:TreeNode>
                            </Nodes>
                        </asp:TreeView>
                    </td>
                    <td class="auto-right" valign="top">
                        <br />
                        <asp:ContentPlaceHolder ID="ContentPlaceHolder1" runat="server">
                        </asp:ContentPlaceHolder>
                    </td>
                </tr>
                <tr>
                    <td class="auto-style7">&nbsp;</td>
                    <td class="auto-style7">Copyright 2025</td>
                </tr>
            </table>
   
        </div>
    </form>
</body>
</html>
