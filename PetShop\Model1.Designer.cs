//------------------------------------------------------------------------------
// <auto-generated>
//     此代码已从模板生成。
//
//     手动更改此文件可能导致应用程序出现意外的行为。
//     如果重新生成代码，将覆盖对此文件的手动更改。
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Data.Entity;
using System.Data.Entity.Infrastructure;
using System.Data.Entity.Core.EntityClient;

namespace PetShop
{
    /// <summary>
    /// 没有可用的元数据文档。
    /// </summary>
    public partial class MyPetShopEntities : DbContext
    {
        /// <summary>
        /// 使用连接字符串"name=MyPetShopEntities"初始化新的 MyPetShopEntities 对象。
        /// </summary>
        public MyPetShopEntities() : base("name=MyPetShopEntities")
        {
        }
    
        /// <summary>
        /// 使用现有的连接初始化新的 MyPetShopEntities 对象。
        /// 如果 contextOwnsConnection 为 true，则释放上下文时将释放连接。
        /// </summary>
        public MyPetShopEntities(EntityConnection connection, bool contextOwnsConnection) : base(connection, contextOwnsConnection)
        {
        }
    }
}
