﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web1
{
    public partial class productAdd : System.Web.UI.Page
    {
        MyPetShopEntities db = new MyPetShopEntities();
        protected void Page_Load(object sender, EventArgs e)
        {
            bindddl();
            this.ddl_cate.SelectedIndex = 0;
            this.ddl_supp.SelectedIndex = 0;
        }

        public void bindddl()
        {
            this.ddl_supp.DataSource = db.Supplier.ToList();

            this.ddl_supp.DataTextField = "Name";
            this.ddl_supp.DataValueField = "SuppId";
            this.ddl_supp.DataBind();
        }

        protected void Button2_Click(object sender, EventArgs e)
        {//添加功能
            Product p = new Product();
            p.CategoryId =int.Parse( this.ddl_cate.SelectedItem.Value);
            p.SuppId = int.Parse(this.ddl_supp.SelectedItem.Value);
            p.ListPrice =Convert.ToDecimal( this.txt_list.Text.Trim());
            p.UnitCost = Convert.ToDecimal(this.txt_unit.Text.Trim());
            p.Name = txt_name.Text.Trim();
            p.Descn = txt_desp.Text.Trim();
            p.Qty =Convert.ToInt16( txt_qty.Text);

            p.Image = this.Image1.ImageUrl;

            db.Product.Add(p);
            db.SaveChanges();

            Response.Redirect("productList.aspx");
        }

        protected void Button1_Click(object sender, EventArgs e)
        {
            //图片上传

           if(FileUpload1.HasFile)
            {
                //this.Label2.Text = FileUpload1.PostedFile.FileName;
               string filepath= FileUpload1.PostedFile.FileName;

                string filename = filepath.Substring(filepath.LastIndexOf("\\")+1);
                string fileindex = filename.Substring(filename.LastIndexOf("."));

                if (fileindex == ".gif")
                {
                    //this.Label2.Text=  Server.MapPath("Prod_Images");
                  string newfilepath=  Server.MapPath("Prod_Images")+filename;
                    this.FileUpload1.PostedFile.SaveAs(newfilepath);
                    this.Image1.ImageUrl = "Prod_Images//"+filename;
                }
                else
                {
                    this.Label2.Text = "图片格式必须为gif";
                }

            }
        }
    }
}