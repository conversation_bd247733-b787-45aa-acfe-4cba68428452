﻿<?xml version="1.0" encoding="utf-8"?>
<Schema Namespace="MyPetShopModel" Alias="Self" annotation:UseStrongSpatialTypes="false" xmlns:annotation="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns:customannotation="http://schemas.microsoft.com/ado/2013/11/edm/customannotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm">
  <EntityType Name="CartItem">
    <Key>
      <PropertyRef Name="CartItemId" />
    </Key>
    <Property Name="CartItemId" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
    <Property Name="CustomerId" Type="Int32" Nullable="false" />
    <Property Name="ProId" Type="Int32" Nullable="false" />
    <Property Name="ProName" Type="String" MaxLength="80" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="ListPrice" Type="Decimal" Precision="10" Scale="2" Nullable="false" />
    <Property Name="Qty" Type="Int32" Nullable="false" />
    <NavigationProperty Name="Customer" Relationship="Self.FK__CartItem__Custom__32E0915F" FromRole="CartItem" ToRole="Customer" />
    <NavigationProperty Name="Product" Relationship="Self.FK__CartItem__ProId__33D4B598" FromRole="CartItem" ToRole="Product" />
  </EntityType>
  <EntityType Name="Category">
    <Key>
      <PropertyRef Name="CategoryId" />
    </Key>
    <Property Name="CategoryId" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
    <Property Name="Name" Type="String" MaxLength="80" FixedLength="false" Unicode="true" />
    <Property Name="Descn" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
    <NavigationProperty Name="Product" Relationship="Self.FK__Product__Categor__2F10007B" FromRole="Category" ToRole="Product" />
  </EntityType>
  <EntityType Name="Customer">
    <Key>
      <PropertyRef Name="CustomerId" />
    </Key>
    <Property Name="CustomerId" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
    <Property Name="Name" Type="String" MaxLength="80" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="Password" Type="String" MaxLength="80" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="Email" Type="String" MaxLength="80" FixedLength="false" Unicode="true" Nullable="false" />
    <NavigationProperty Name="CartItem" Relationship="Self.FK__CartItem__Custom__32E0915F" FromRole="Customer" ToRole="CartItem" />
    <NavigationProperty Name="Order" Relationship="Self.FK__Order__CustomerI__276EDEB3" FromRole="Customer" ToRole="Order" />
  </EntityType>
  <EntityType Name="Order">
    <Key>
      <PropertyRef Name="OrderId" />
    </Key>
    <Property Name="OrderId" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
    <Property Name="CustomerId" Type="Int32" Nullable="false" />
    <Property Name="UserName" Type="String" MaxLength="80" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="OrderDate" Type="DateTime" Nullable="false" Precision="3" />
    <Property Name="Addr1" Type="String" MaxLength="80" FixedLength="false" Unicode="true" />
    <Property Name="Addr2" Type="String" MaxLength="80" FixedLength="false" Unicode="true" />
    <Property Name="City" Type="String" MaxLength="80" FixedLength="false" Unicode="true" />
    <Property Name="State" Type="String" MaxLength="80" FixedLength="false" Unicode="true" />
    <Property Name="Zip" Type="String" MaxLength="6" FixedLength="false" Unicode="true" />
    <Property Name="Phone" Type="String" MaxLength="40" FixedLength="false" Unicode="true" />
    <Property Name="Status" Type="String" MaxLength="10" FixedLength="false" Unicode="true" />
    <NavigationProperty Name="Customer" Relationship="Self.FK__Order__CustomerI__276EDEB3" FromRole="Order" ToRole="Customer" />
    <NavigationProperty Name="OrderItem" Relationship="Self.FK__OrderItem__Order__2A4B4B5E" FromRole="Order" ToRole="OrderItem" />
  </EntityType>
  <EntityType Name="OrderItem">
    <Key>
      <PropertyRef Name="ItemId" />
    </Key>
    <Property Name="ItemId" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
    <Property Name="OrderId" Type="Int32" Nullable="false" />
    <Property Name="ProName" Type="String" MaxLength="80" FixedLength="false" Unicode="true" />
    <Property Name="ListPrice" Type="Decimal" Precision="10" Scale="2" />
    <Property Name="Qty" Type="Int32" Nullable="false" />
    <Property Name="TotalPrice" Type="Decimal" Precision="10" Scale="2" />
    <NavigationProperty Name="Order" Relationship="Self.FK__OrderItem__Order__2A4B4B5E" FromRole="OrderItem" ToRole="Order" />
  </EntityType>
  <EntityType Name="Product">
    <Key>
      <PropertyRef Name="ProductId" />
    </Key>
    <Property Name="ProductId" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
    <Property Name="CategoryId" Type="Int32" Nullable="false" />
    <Property Name="ListPrice" Type="Decimal" Precision="10" Scale="2" />
    <Property Name="UnitCost" Type="Decimal" Precision="10" Scale="2" />
    <Property Name="SuppId" Type="Int32" />
    <Property Name="Name" Type="String" MaxLength="80" FixedLength="false" Unicode="true" />
    <Property Name="Descn" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
    <Property Name="Image" Type="String" MaxLength="80" FixedLength="false" Unicode="true" />
    <Property Name="Qty" Type="Int32" Nullable="false" />
    <NavigationProperty Name="CartItem" Relationship="Self.FK__CartItem__ProId__33D4B598" FromRole="Product" ToRole="CartItem" />
    <NavigationProperty Name="Category" Relationship="Self.FK__Product__Categor__2F10007B" FromRole="Product" ToRole="Category" />
    <NavigationProperty Name="Supplier" Relationship="Self.FK__Product__SuppId__300424B4" FromRole="Product" ToRole="Supplier" />
  </EntityType>
  <EntityType Name="Supplier">
    <Key>
      <PropertyRef Name="SuppId" />
    </Key>
    <Property Name="SuppId" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
    <Property Name="Name" Type="String" MaxLength="80" FixedLength="false" Unicode="true" />
    <Property Name="Addr1" Type="String" MaxLength="80" FixedLength="false" Unicode="true" />
    <Property Name="Addr2" Type="String" MaxLength="80" FixedLength="false" Unicode="true" />
    <Property Name="City" Type="String" MaxLength="80" FixedLength="false" Unicode="true" />
    <Property Name="State" Type="String" MaxLength="80" FixedLength="false" Unicode="true" />
    <Property Name="Zip" Type="String" MaxLength="6" FixedLength="false" Unicode="true" />
    <Property Name="Phone" Type="String" MaxLength="40" FixedLength="false" Unicode="true" />
    <NavigationProperty Name="Product" Relationship="Self.FK__Product__SuppId__300424B4" FromRole="Supplier" ToRole="Product" />
  </EntityType>
  <Association Name="FK__CartItem__Custom__32E0915F">
    <End Role="Customer" Type="Self.Customer" Multiplicity="1" />
    <End Role="CartItem" Type="Self.CartItem" Multiplicity="*" />
    <ReferentialConstraint>
      <Principal Role="Customer">
        <PropertyRef Name="CustomerId" />
      </Principal>
      <Dependent Role="CartItem">
        <PropertyRef Name="CustomerId" />
      </Dependent>
    </ReferentialConstraint>
  </Association>
  <Association Name="FK__CartItem__ProId__33D4B598">
    <End Role="Product" Type="Self.Product" Multiplicity="1" />
    <End Role="CartItem" Type="Self.CartItem" Multiplicity="*" />
    <ReferentialConstraint>
      <Principal Role="Product">
        <PropertyRef Name="ProductId" />
      </Principal>
      <Dependent Role="CartItem">
        <PropertyRef Name="ProId" />
      </Dependent>
    </ReferentialConstraint>
  </Association>
  <Association Name="FK__Product__Categor__2F10007B">
    <End Role="Category" Type="Self.Category" Multiplicity="1" />
    <End Role="Product" Type="Self.Product" Multiplicity="*" />
    <ReferentialConstraint>
      <Principal Role="Category">
        <PropertyRef Name="CategoryId" />
      </Principal>
      <Dependent Role="Product">
        <PropertyRef Name="CategoryId" />
      </Dependent>
    </ReferentialConstraint>
  </Association>
  <Association Name="FK__Order__CustomerI__276EDEB3">
    <End Role="Customer" Type="Self.Customer" Multiplicity="1" />
    <End Role="Order" Type="Self.Order" Multiplicity="*" />
    <ReferentialConstraint>
      <Principal Role="Customer">
        <PropertyRef Name="CustomerId" />
      </Principal>
      <Dependent Role="Order">
        <PropertyRef Name="CustomerId" />
      </Dependent>
    </ReferentialConstraint>
  </Association>
  <Association Name="FK__OrderItem__Order__2A4B4B5E">
    <End Role="Order" Type="Self.Order" Multiplicity="1" />
    <End Role="OrderItem" Type="Self.OrderItem" Multiplicity="*" />
    <ReferentialConstraint>
      <Principal Role="Order">
        <PropertyRef Name="OrderId" />
      </Principal>
      <Dependent Role="OrderItem">
        <PropertyRef Name="OrderId" />
      </Dependent>
    </ReferentialConstraint>
  </Association>
  <Association Name="FK__Product__SuppId__300424B4">
    <End Role="Supplier" Type="Self.Supplier" Multiplicity="0..1" />
    <End Role="Product" Type="Self.Product" Multiplicity="*" />
    <ReferentialConstraint>
      <Principal Role="Supplier">
        <PropertyRef Name="SuppId" />
      </Principal>
      <Dependent Role="Product">
        <PropertyRef Name="SuppId" />
      </Dependent>
    </ReferentialConstraint>
  </Association>
  <EntityContainer Name="MyPetShopEntities" annotation:LazyLoadingEnabled="true">
    <EntitySet Name="CartItem" EntityType="Self.CartItem" />
    <EntitySet Name="Category" EntityType="Self.Category" />
    <EntitySet Name="Customer" EntityType="Self.Customer" />
    <EntitySet Name="Order" EntityType="Self.Order" />
    <EntitySet Name="OrderItem" EntityType="Self.OrderItem" />
    <EntitySet Name="Product" EntityType="Self.Product" />
    <EntitySet Name="Supplier" EntityType="Self.Supplier" />
    <AssociationSet Name="FK__CartItem__Custom__32E0915F" Association="Self.FK__CartItem__Custom__32E0915F">
      <End Role="Customer" EntitySet="Customer" />
      <End Role="CartItem" EntitySet="CartItem" />
    </AssociationSet>
    <AssociationSet Name="FK__CartItem__ProId__33D4B598" Association="Self.FK__CartItem__ProId__33D4B598">
      <End Role="Product" EntitySet="Product" />
      <End Role="CartItem" EntitySet="CartItem" />
    </AssociationSet>
    <AssociationSet Name="FK__Product__Categor__2F10007B" Association="Self.FK__Product__Categor__2F10007B">
      <End Role="Category" EntitySet="Category" />
      <End Role="Product" EntitySet="Product" />
    </AssociationSet>
    <AssociationSet Name="FK__Order__CustomerI__276EDEB3" Association="Self.FK__Order__CustomerI__276EDEB3">
      <End Role="Customer" EntitySet="Customer" />
      <End Role="Order" EntitySet="Order" />
    </AssociationSet>
    <AssociationSet Name="FK__OrderItem__Order__2A4B4B5E" Association="Self.FK__OrderItem__Order__2A4B4B5E">
      <End Role="Order" EntitySet="Order" />
      <End Role="OrderItem" EntitySet="OrderItem" />
    </AssociationSet>
    <AssociationSet Name="FK__Product__SuppId__300424B4" Association="Self.FK__Product__SuppId__300424B4">
      <End Role="Supplier" EntitySet="Supplier" />
      <End Role="Product" EntitySet="Product" />
    </AssociationSet>
  </EntityContainer>
</Schema>