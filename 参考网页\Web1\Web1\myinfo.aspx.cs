﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web1
{
    public partial class myinfo : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                // 检查用户是否已登录
                if (Session["UserId"] == null)
                {
                    Response.Redirect("login.aspx");
                    return;
                }

                // 显示用户信息
                LoadUserInfo();
            }
        }

        private void LoadUserInfo()
        {
            try
            {
                int userId = Convert.ToInt32(Session["UserId"]);
                string userName = Session["UserName"].ToString();
                string userEmail = Session["UserEmail"].ToString();

                lbl_userid.Text = userId.ToString();
                lbl_username.Text = userName;
                lbl_email.Text = userEmail;

                // 如果是管理员，显示"登入到后台"按钮
                if (userName.ToLower() == "admin")
                {
                    btn_admin.Visible = true;
                }
            }
            catch (Exception ex)
            {
                lbl_message.Text = "加载用户信息失败：" + ex.Message;
            }
        }

        protected void btn_admin_Click(object sender, EventArgs e)
        {
            // 跳转到后台管理页面
            Response.Redirect("index_m.aspx");
        }

        protected void btn_logout_Click(object sender, EventArgs e)
        {
            // 清除Session，退出登录
            Session.Clear();
            Session.Abandon();
            Response.Redirect("login.aspx");
        }

        protected void btn_back_Click(object sender, EventArgs e)
        {
            // 返回首页
            Response.Redirect("index.aspx");
        }
    }
}