﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web1
{
    public partial class productCate : System.Web.UI.Page
    {
        MyPetShopEntities db = new MyPetShopEntities();
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                string pid = Request.QueryString["cid"].ToString();
                int ppid = int.Parse(pid);
                var re = db.Product.Where(p => p.CategoryId == ppid);

                this.DataList2.DataSource = re.ToList();
                this.DataList2.DataBind();
            }
        }
    }
}